#!/usr/bin/env python3
"""
测试 user_relationships 表的插入和查询功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.repositories.user_repository import UserRepository

def test_user_relationships_table():
    """测试 user_relationships 表"""
    print("🧪 测试 user_relationships 表...")
    
    user_repo = UserRepository()
    
    # 准备测试数据
    test_relationships = [
        {
            'parent_digital_id': 'parent_001',
            'child_digital_id': 'child_001',
            'relationship_type': 'direct_agent',
            'level_diff': 1
        },
        {
            'parent_digital_id': 'parent_001',
            'child_digital_id': 'child_002',
            'relationship_type': 'direct_agent',
            'level_diff': 1
        },
        {
            'parent_digital_id': 'child_001',
            'child_digital_id': 'child_003',
            'relationship_type': 'sub_agent',
            'level_diff': 2
        }
    ]
    
    # 测试批量插入功能
    try:
        user_repo.batch_insert_relationships(test_relationships)
        print(f"✅ user_relationships 表批量插入测试成功，共 {len(test_relationships)} 条记录")
    except Exception as e:
        print(f"❌ user_relationships 表批量插入测试失败: {e}")
        return False
    
    # 验证数据插入
    try:
        from database.duckdb_manager import db_manager
        result = db_manager.execute_sql("SELECT COUNT(*) as count FROM user_relationships WHERE parent_digital_id LIKE 'parent_%' OR parent_digital_id LIKE 'child_%'")
        count = result[0]['count'] if result else 0
        print(f"✅ 验证成功，数据库中有 {count} 条测试记录")
        
        if count != len(test_relationships):
            print(f"❌ 数据数量不匹配，期望 {len(test_relationships)} 条，实际 {count} 条")
            return False
            
    except Exception as e:
        print(f"❌ 验证数据插入失败: {e}")
        return False
    
    # 测试查询功能 - 先创建测试用户数据
    try:
        # 创建测试用户数据（user_relationships 表需要对应的用户存在）
        test_users = [
            {
                'digital_id': 'parent_001',
                'member_id': 'member_parent_001',
                'user_name': '测试父用户',
                'agent_flag': 'BD',
                'bd_name': 'BD_TEST'
            },
            {
                'digital_id': 'child_001',
                'member_id': 'member_child_001',
                'user_name': '测试子用户1',
                'agent_flag': '1级代理',
                'bd_name': 'BD_TEST'
            },
            {
                'digital_id': 'child_002',
                'member_id': 'member_child_002',
                'user_name': '测试子用户2',
                'agent_flag': '1级代理',
                'bd_name': 'BD_TEST'
            }
        ]

        user_repo.batch_insert_users(test_users)
        print(f"✅ 创建测试用户数据成功")

    except Exception as e:
        print(f"❌ 创建测试用户数据失败: {e}")
        return False
    
    # 测试层级查询
    try:
        # 查询用户的完整层级结构
        hierarchy = user_repo.get_user_hierarchy('parent_001')
        print(f"✅ 层级查询成功")

        # 验证层级结构
        if hierarchy and 'children' in hierarchy:
            children = hierarchy['children']
            print(f"  - 根节点: parent_001")
            print(f"  - 直接子节点数: {len(children)}")

            # 验证子节点
            expected_children = ['child_001', 'child_002']
            actual_children = [child['user_info']['digital_id'] for child in children if 'user_info' in child]

            if set(actual_children) == set(expected_children):
                print("✅ 层级查询结果验证通过")
            else:
                print(f"❌ 层级查询结果验证失败，期望: {expected_children}，实际: {actual_children}")
                return False

        else:
            print(f"❌ 层级结构验证失败")
            return False

    except Exception as e:
        print(f"❌ 层级查询测试失败: {e}")
        return False
    
    return True

def cleanup_test_data():
    """清理测试数据"""
    print("\n🗑️  清理测试数据...")

    try:
        from database.duckdb_manager import db_manager
        # 清理关系数据
        db_manager.execute_sql_no_return("DELETE FROM user_relationships WHERE parent_digital_id LIKE 'parent_%' OR parent_digital_id LIKE 'child_%'")
        # 清理用户数据
        db_manager.execute_sql_no_return("DELETE FROM users WHERE digital_id LIKE 'parent_%' OR digital_id LIKE 'child_%'")
        print("✅ 测试数据清理完成")
    except Exception as e:
        print(f"⚠️  清理测试数据失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试 user_relationships 表功能\n")
    
    # 测试功能
    test_passed = test_user_relationships_table()
    
    # 清理测试数据
    cleanup_test_data()
    
    print(f"\n📋 测试总结:")
    print(f"  - user_relationships 表测试: {'✅ 通过' if test_passed else '❌ 失败'}")
    
    if test_passed:
        print("\n🎉 user_relationships 表功能测试通过！")
    else:
        print("\n⚠️  user_relationships 表功能测试失败，需要进一步检查。")
