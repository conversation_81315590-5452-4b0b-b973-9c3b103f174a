#!/usr/bin/env python3
"""
测试 agent_analysis 表的插入和查询功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.repositories.agent_repository import AgentRepository
import uuid

def test_agent_analysis_table():
    """测试 agent_analysis 表"""
    print("🧪 测试 agent_analysis 表...")
    
    agent_repo = AgentRepository()
    
    # 准备测试数据
    test_task_id = str(uuid.uuid4())
    test_analysis_type = 'agent_relationship'
    test_filename = 'test_data.csv'
    test_total_users = 100
    test_device_shared_count = 15
    test_ip_shared_count = 8
    test_both_shared_count = 3
    test_result_data = {
        'device_shared': [{'user_a': 'user1', 'user_b': 'user2', 'shared_device': 'device123'}],
        'ip_shared': [{'user_a': 'user3', 'user_b': 'user4', 'shared_ip': '***********'}],
        'both_shared': [{'user_a': 'user5', 'user_b': 'user6'}],
        'bd_statistics': {'BD_A': 50, 'BD_B': 30, 'BD_C': 20}
    }
    
    # 测试插入功能
    try:
        success = agent_repo.save_analysis_result(
            task_id=test_task_id,
            analysis_type=test_analysis_type,
            filename=test_filename,
            total_users=test_total_users,
            device_shared_count=test_device_shared_count,
            ip_shared_count=test_ip_shared_count,
            both_shared_count=test_both_shared_count,
            result_data=test_result_data
        )
        
        if success:
            print(f"✅ agent_analysis 表插入测试成功")
        else:
            print(f"❌ agent_analysis 表插入测试失败")
            return False
            
    except Exception as e:
        print(f"❌ agent_analysis 表插入测试异常: {e}")
        return False
    
    # 测试查询功能
    try:
        result = agent_repo.get_analysis_result(test_task_id)
        
        if result:
            print(f"✅ agent_analysis 表查询测试成功")
            print(f"  - 任务ID: {result.get('task_id')}")
            print(f"  - 分析类型: {result.get('analysis_type')}")
            print(f"  - 文件名: {result.get('filename')}")
            print(f"  - 总用户数: {result.get('total_users')}")
            print(f"  - 设备共享数: {result.get('device_shared_count')}")
            print(f"  - IP共享数: {result.get('ip_shared_count')}")
            print(f"  - 双重共享数: {result.get('both_shared_count')}")
            
            # 验证数据完整性
            if (result.get('task_id') == test_task_id and
                result.get('analysis_type') == test_analysis_type and
                result.get('total_users') == test_total_users and
                result.get('device_shared_count') == test_device_shared_count):
                print("✅ 数据完整性验证通过")
            else:
                print("❌ 数据完整性验证失败")
                return False
        else:
            print(f"❌ agent_analysis 表查询测试失败：未找到记录")
            return False
            
    except Exception as e:
        print(f"❌ agent_analysis 表查询测试异常: {e}")
        return False
    
    # 测试更新功能
    try:
        updated_total_users = 150
        success = agent_repo.save_analysis_result(
            task_id=test_task_id,  # 相同的task_id，应该触发更新
            analysis_type=test_analysis_type,
            filename=test_filename,
            total_users=updated_total_users,  # 更新的值
            device_shared_count=test_device_shared_count,
            ip_shared_count=test_ip_shared_count,
            both_shared_count=test_both_shared_count,
            result_data=test_result_data
        )
        
        if success:
            # 验证更新是否成功
            updated_result = agent_repo.get_analysis_result(test_task_id)
            if updated_result and updated_result.get('total_users') == updated_total_users:
                print(f"✅ agent_analysis 表更新测试成功")
            else:
                print(f"❌ agent_analysis 表更新测试失败：数据未正确更新")
                return False
        else:
            print(f"❌ agent_analysis 表更新测试失败")
            return False
            
    except Exception as e:
        print(f"❌ agent_analysis 表更新测试异常: {e}")
        return False
    
    return True

def cleanup_test_data():
    """清理测试数据"""
    print("\n🗑️  清理测试数据...")
    
    try:
        from database.duckdb_manager import db_manager
        # 清理测试数据（删除所有测试记录）
        db_manager.execute_sql_no_return("DELETE FROM agent_analysis WHERE analysis_type = 'agent_relationship'")
        print("✅ 测试数据清理完成")
    except Exception as e:
        print(f"⚠️  清理测试数据失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试 agent_analysis 表功能\n")
    
    # 测试功能
    test_passed = test_agent_analysis_table()
    
    # 清理测试数据
    cleanup_test_data()
    
    print(f"\n📋 测试总结:")
    print(f"  - agent_analysis 表测试: {'✅ 通过' if test_passed else '❌ 失败'}")
    
    if test_passed:
        print("\n🎉 agent_analysis 表功能测试通过！")
    else:
        print("\n⚠️  agent_analysis 表功能测试失败，需要进一步检查。")
